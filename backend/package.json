{"name": "backend", "private": true, "version": "7.2.0", "type": "module", "scripts": {"install:dependencies": "cd ../packages/currency-converter && npm i && cd ../bookcars-helper && npm i", "ts:build": "npm run install:dependencies && tsc --build --verbose", "build": "npm run ts:build && cross-env NODE_OPTIONS=--max-old-space-size=1024 vite build", "dev": "npm run ts:build && vite", "ts:docker-build": "tsc --build --verbose", "dev:docker": "npm run ts:docker-build && vite", "preview": "vite preview", "fix": "eslint --fix .", "lint": "eslint . --cache --cache-location .eslintcache", "ncu": "ncu -u", "stylelint": "stylelint \"src/**/*.css\"", "stylelint:fix": "stylelint \"src/**/*.css\" --fix"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.3.0", "@mui/x-date-pickers": "^8.3.0", "@types/nprogress": "^0.2.3", "@types/react-dom": "^19.1.5", "@types/validator": "^13.15.0", "axios": "^1.9.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "eslint-plugin-react-refresh": "^0.4.20", "history": "^5.3.0", "localized-strings": "^2.0.3", "nprogress": "^0.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "rrule": "^2.8.1", "typescript": "^5.8.3", "validator": "^13.15.0", "zod": "^3.24.4"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.1", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.4.1", "babel-plugin-react-compiler": "^19.1.0-rc.1", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.1", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "npm-check-updates": "^18.0.1", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "terser": "^5.39.1", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2"}}