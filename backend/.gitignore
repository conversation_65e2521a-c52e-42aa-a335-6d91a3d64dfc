# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# testing
/coverage

# production
/build

# misc
.DS_Store
.DS_Store?
.env
.env.local
.env.dev
.env.development
.env.development.local
.env.test.local
.env.*production*
.env.docker
.env.docker.development

npm-debug.log*
yarn-debug.log*
yarn-error.log*

*.tsbuildinfo
vite.config.d.ts
vite.config.js
.cache/

.eslintcache
