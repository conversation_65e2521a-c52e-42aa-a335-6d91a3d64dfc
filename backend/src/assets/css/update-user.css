/* Update User */
div.update-user {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  align-items: center;
  transform: translate3d(0, 0, 0);
  margin: 45px 0;
}

div.update-user .user-form-wrapper {
  margin: 32px 0;
}

div.update-user .user-form-title {
  text-align: center;
  text-transform: capitalize;
  color: #121212;
}

div.update-user .resend-activation-link {
  cursor: pointer;
  margin: 20px 0 10px;
}

div.update-user .driver-license-field {
  margin-top: 15px;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.update-user .user-form {
    width: 360px;
    padding: 30px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.update-user .user-form {
    width: 550px;
    padding: 30px;
  }
}
