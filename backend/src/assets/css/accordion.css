div.accordion-container {
  background: #fff;
  margin: 10px 0;
  border: 1px solid #dadada;
  border-radius: 5px;
  font-size: 13px;
  user-select: none;
}

div.accordion-container span.accordion {
  cursor: pointer;
  padding: 2px;
  transition: 0.4s;
  display: inline-block;
  width: 100%;
  text-align: center;
  color: rgb(0 0 0 / 60%);
  font-weight: 400;
  font-size: 13px;
  line-height: 2em;
  background: #fff;
  border-radius: 5px;
}

div.accordion-container span.accordion-active {
  border-bottom: 1px solid #dadada;
  border-radius: 5px 5px 0 0;
}

div.accordion-container span.accordion-active,
div.accordion-container span.accordion:hover {
  background: #fff;
}

div.accordion-container span.accordion::after {
  content: '';
  border-top: 5px solid transparent;
  border-left: 5px solid #000;
  border-bottom: 5px solid transparent;
  transition: transform 0.1s ease;
  float: right;
  margin: 7px 7px 0;
}

div.accordion-container span.accordion-active::after {
  transform: rotate(90deg);
}

div.accordion-container div.panel {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.18s ease-out;
}

div.accordion-container div.panel-collapse {
  overflow: hidden;
}
