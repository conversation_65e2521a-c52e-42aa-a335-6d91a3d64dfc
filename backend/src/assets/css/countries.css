div.countries {
  display: flex;
  flex: 1 0 auto;
  transform: translate3d(0, 0, 0);
}

div.countries .search {
  width: 100%;
  display: flex;
  justify-content: center;
}

div.countries .info-box {
  background-color: #fff;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.countries {
    flex-direction: column;
  }

  div.countries div.col-1 {
    width: 100%;
    padding: 15px;
    display: flex;
    justify-content: center;
  }

  div.countries div.col-1 div.col-1-container {
    width: 100%;
    max-width: 480px;
  }

  div.countries .sc-search {
    width: 80%;
  }

  div.countries .new-country {
    margin: 20px 5px 5px 0;
    width: 100%;
  }

  div.countries div.col-2 {
    display: flex;
    flex-direction: column;
    margin-top: 3px;
    padding: 1px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.countries {
    flex-direction: row;
  }

  div.countries div.col-1 {
    float: left;
    width: 300px;
    margin-left: 20px;
  }

  div.countries div.col-2 {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
  }

  div.countries .search {
    margin-top: 15px;
  }

  div.countries .new-country {
    margin: 10px 5px 0 0;
    width: 100%;
  }
}
