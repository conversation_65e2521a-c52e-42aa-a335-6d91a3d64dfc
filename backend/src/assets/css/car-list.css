section.car-list {
  display: flex;
  flex-direction: column;
  align-items: center;
}

section.car-list .empty-list {
  text-align: center;
  width: 250px;
}

section.car-list article span.coming-soon,
section.car-list article span.fully-booked {
  font-size: 12px;
  font-weight: bold;
  display: table-row;
  text-align: right;
}

section.car-list article span.coming-soon {
  color: #FD3446;
}

section.car-list article span.fully-booked {
  color: #FD3446;
}

section.car-list div.car-header {
  padding: 10px 0;
  border-bottom: #d9d8d9 2px solid;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}

section.car-list div.car-header div.location {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #232323;
  font-size: 18px;
}

section.car-list div.car-header div.location span.location-name {
  margin-left: 5px;
}

section.car-list div.car-header div.distance {
  display: flex;
  flex-direction: row;
  align-items: center;
}

section.car-list div.car-header div.distance img {
  max-width: 16px;
  max-height: 16px;
  margin-right: 5px;
}

section.car-list {
  padding-top: 10px;
}

section.car-list article {
  background: #fff;
  border: 1px solid #eee;
  border-radius: 5px;
  color: #333;
  font-size: 12px;
  margin-bottom: 10px;
  width: 800px;
  display: flex;
  flex-direction: row;
  padding: 15px;
}

section.car-list article div.car {
  padding: 10px;
  width: 290px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  position: relative;
  top: 0;
  overflow: hidden;
}

section.car-list article div.car .car-img {
  margin-top: 15px;
  margin-bottom: 0;
  max-width: 100%;
  max-height: 100%;
}

section.car-list article div.car .car-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

section.car-list article div.car .car-footer .car-footer-row1 {
  display: flex;
  flex-direction: row;
  margin-bottom: 5px;
}

section.car-list article div.car .car-footer div.rating,
section.car-list article div.car .car-footer div.co2 {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 5px;
}

section.car-list article div.car .car-footer div.rating img {
  max-width: 16px;
  max-height: 16px;
  margin: 0 5px;
}

section.car-list article div.car .car-footer div.rating span.value {
  font-weight: 600;
}

section.car-list article div.car .car-footer div.rating span.trips {
  color: #A2A2A2;
  font-size: 11px;
}

section.car-list article div.car .car-footer div.co2 {
  margin-left: 20px;
}

section.car-list article div.car .car-footer div.co2 span {
  font-weight: 600;
}

section.car-list article div.car .car-footer div.co2 img {
  max-width: 19px;
  max-height: 5px;
  margin-right: 5px;
}

section.car-list article div.car-info {
  /* width: 100%; */
  width: 510px;
}

section.car-list article div.car-info div.car-info-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

section.car-list article div.car-info div.car-info-header div.name {
  padding: 0 11px;
  pointer-events: none;

  font-size: 1.4em;
  font-weight: 700;
  text-size-adjust: none;
  line-height: 1.3em;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

section.car-list article div.price {
  display: table;
  border-collapse: separate;
  color: #383838;
  font-size: 2em;
  font-weight: 700;
  white-space: nowrap;
}

section.car-list article div.car-info ul.car-info-list {
  position: relative;
  list-style: none;
  padding: 0;
  display: flex;
  flex-flow: row wrap;
}

section.car-list article div.car-info ul.car-info-list li.car-type,
section.car-list article div.car-info ul.car-info-list li.gearbox,
section.car-list article div.car-info ul.car-info-list li.seats,
section.car-list article div.car-info ul.car-info-list li.doors,
section.car-list article div.car-info ul.car-info-list li.aircon {
  width: 60px;
  margin-bottom: 10px;
  padding-top: 3px;
  box-shadow: 0 0 0 1px #ddd inset;
  margin-left: 5px;
  border-radius: 5px;
  text-align: center;
}

section.car-list article div.car-info .accordion {
  box-shadow: none;
  border: none;
  margin-bottom: 20px;
}

section.car-list article div.car-info .accordion::before {
  background-color: #fff;
}

section.car-list article div.car-info .accordion-summary {
  border-bottom: #CFD8DC 1px solid;
  font-weight: 600;
}

section.car-list article div.car-info ul.extras-list {
  position: relative;
  list-style: none;
  padding: 0;
}

section.car-list article div.car-info ul.extras-list li {
  margin: 5px;
  width: 100%;
}

section.car-list article div.car-info ul.extras-list li.car-available {
  color: #1f9201;
}

section.car-list article div.car-info ul.extras-list li.car-coming-soon {
  color: #1f9201;
}

section.car-list article div.car-info ul.extras-list li.car-unavailable {
  color: #f44336;
}

section.car-list article div.car-info ul.extras-list span.car-info-list-text {
  vertical-align: super;
  margin-left: 3px;
}

section.car-list article div.car-info ul.car-info-list div.car-info-list-item {
  color: #000;
}

section.car-list article div.car-info ul.car-info-list li.mileage div.car-info-list-item,
section.car-list article div.car-info ul.car-info-list li.fuel-policy div.car-info-list-item {
  width: fit-content;
}

section.car-list article div.car-info ul.car-info-list span.car-info-list-text {
  vertical-align: super;
  margin-left: 3px;
}

section.car-list article div.car-info ul.car-info-list li .car-doors {
  width: 20px;
  height: 20px;
  margin: 2px;
}

section.car-list article div.action {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  section.car-list {
    width: 100%;
    max-width: 600px;
  }

  section.car-list article {
    flex-direction: column;
    align-items: flex-start;
    width: calc(100% - 20px);
  }

  section.car-list div.car-header {
    width: calc(100% - 20px);
  }

  section.car-list article div.car-info {
    width: 100%;
  }
}
