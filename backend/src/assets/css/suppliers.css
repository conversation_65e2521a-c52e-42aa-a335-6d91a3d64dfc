div.suppliers {
  display: flex;
  flex: 1 0 auto;
  transform: translate3d(0, 0, 0);
}

div.suppliers .search {
  width: 100%;
  display: flex;
  justify-content: center;
}

div.suppliers .info-box {
  background-color: #fff;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.suppliers {
    flex-direction: column;
  }

  div.suppliers div.col-1 {
    width: 100%;
    padding: 15px;
    display: flex;
    justify-content: center;
  }

  div.suppliers div.col-1 div.col-1-container {
    width: 100%;
    max-width: 480px;
  }

  div.suppliers .sc-search {
    width: 80%;
  }

  div.suppliers .new-supplier {
    margin: 20px 5px 5px 0;
    width: 100%;
  }

  div.suppliers div.col-2 {
    margin-top: 3px;
    padding: 1px;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.suppliers {
    flex-direction: row;
  }

  div.suppliers div.col-1 {
    width: 300px;
    margin-left: 20px;
  }

  div.suppliers div.col-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }

  div.suppliers .search {
    margin-top: 15px;
  }

  div.suppliers .new-supplier {
    margin: 10px 5px 0 0;
    width: 100%;
  }
}
