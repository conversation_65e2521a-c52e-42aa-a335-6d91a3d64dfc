div.supplier {
  display: flex;
  flex: 1 0 auto;
  transform: translate3d(0, 0, 0);
}

.supplier div.col-1 .supplier-name {
  text-align: center;
  font-weight: 600;
  margin-top: 15px;
  word-wrap: break-word;
}

.supplier div.col-1 .supplier-info {
  text-align: center;
  font-weight: 400;
  margin-top: 10px;
  color: #676767;
  word-wrap: break-word;
}

.supplier div.col-1 .supplier-bio-link {
  overflow: hidden;
  word-break: break-word;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-align: center;
  padding: 0 10px;
}

.supplier div.col-1 .supplier-actions {
  text-align: center;
}

.supplier div.col-1 div.car-supplier {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.supplier div.col-1 div.car-supplier span.car-supplier-logo {
  display: flex;
  overflow: hidden;
  vertical-align: text-bottom;
}

.supplier div.col-1 div.car-supplier span.car-supplier-info {
  color: #a8a8a8;
  display: inline-block;
  font-size: 0.9em;
  line-height: 1em;
  white-space: nowrap;
  margin-left: 5px;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.supplier {
    flex-direction: column;
  }

  .supplier div.col-1 {
    padding: 10px 0;
    background: #fefefe;
    border-bottom: #eee solid 1px;
  }

  .supplier div.col-1 section.supplier-avatar-sec {
    display: flex;
    justify-content: center;
  }

  .supplier div.col-2 {
    display: flex;
    flex-direction: column;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.supplier {
    flex-direction: row;
  }

  .supplier div.col-1 {
    width: 300px;
    min-height: 100vh;
    padding-top: 20px;
    background: #fefefe;
    border-right: #eee solid 1px;
  }

  .supplier div.col-1 section.supplier-avatar-sec {
    position: relative;
    left: 50%;
    transform: translateX(-30%);
  }

  .supplier div.col-1 .car-count {
    margin: 10px 10px 0;
  }

  .supplier div.col-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1;
  }
}
