div.cars {
  display: flex;
  flex: 1 0 auto;
  transform: translate3d(0, 0, 0);
}

div.cars div.search {
  width: 100%;
  display: flex;
  justify-content: center;
}

div.cars div.col-1 .filter,
div.cars div.col-1 div.info-box {
  background-color: #fff;
}

div.cars div.filter-progress-wrapper {
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

div.cars div.filter-progress-wrapper .filter-progress {
  color: #121212;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.cars {
    flex-direction: column;
    flex: 0 1 auto;
  }

  div.cars .sc-search {
    width: 80%;
  }

  div.cars .new-car {
    margin: 20px 5px 5px 0;
    width: 100%;
  }

  div.cars div.col-1 {
    width: 100%;
    padding: 15px;
    display: flex;
    justify-content: center;
  }

  div.cars div.col-1 div.col-1-container {
    width: 100%;
    max-width: 480px;
  }

  div.cars .car-filter {
    text-align: left;
  }

  div.cars div.col-2 {
    margin-top: 3px;
    padding: 1px;
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.cars {
    flex-direction: row;
  }

  div.cars div.col-1 {
    width: 300px;
    margin-left: 20px;
  }

  div.cars div.col-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }

  div.cars div.search {
    margin-top: 15px;
  }

  div.cars .new-car {
    margin: 10px 5px 0 0;
    width: 100%;
  }
}
