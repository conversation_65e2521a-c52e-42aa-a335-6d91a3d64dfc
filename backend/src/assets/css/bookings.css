div.bookings {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
}

/* Device width is lower than or equal to 960px */

@media only screen and (width <= 960px) {
  div.bookings {
    top: 56px;
    overflow-y: auto;
  }

  div.bookings div.col-1,
  div.bookings div.col-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  div.bookings div.col-1 .cl-supplier-filter label.accordion,
  div.bookings div.col-1 .cl-status-filter label.accordion,
  div.bookings div.col-1 .cl-booking-filter label.accordion {
    background: #fff;
  }

  div.bookings div.col-1 .cl-supplier-filter,
  div.bookings div.col-1 .cl-status-filter,
  div.bookings div.col-1 .cl-booking-filter {
    margin: 5px 10px;
    background-color: #fff;
    max-width: 480px;
    width: calc(100% - 20px);
  }

  div.bookings div.col-1 .cl-booking-filter div.panel,
  div.bookings div.col-1 .cl-booking-filter div.panel-collapse {
    padding-right: 15px;
    padding-left: 15px;
  }

  div.bookings div.col-1 .cl-new-booking {
    width: calc(100% - 20px);
    max-width: 480px;
    margin: 15px 10px 5px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >= 960px) {
  div.bookings {
    top: 64px;
  }

  div.bookings div.col-1 {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 300px;
    padding: 12px 0 0 12px;
    background: #fefefe;
    overflow: auto;
  }

  div.bookings div.col-1 .cl-supplier-filter label.accordion,
  div.bookings div.col-1 .cl-status-filter label.accordion,
  div.bookings div.col-1 .cl-booking-filter label.accordion {
    background: #fafafa;
  }

  div.bookings div.col-1 .cl-supplier-filter,
  div.bookings div.col-1 .cl-status-filter,
  div.bookings div.col-1 .cl-booking-filter {
    margin: 10px 10px 10px 0;
    background-color: #fafafa;
  }

  div.bookings div.col-1 .cl-booking-filter div.panel,
  div.bookings div.col-1 .cl-booking-filter div.panel-collapse {
    padding-right: 15px;
    padding-left: 15px;
  }

  div.bookings div.col-1 .cl-status-filter,
  div.bookings div.col-1 .cl-booking-filter {
    margin-bottom: 10px;
  }

  div.bookings div.col-1 .cl-new-booking {
    width: 265px;
    margin-left: 5px;
  }

  div.bookings div.col-2 {
    position: absolute;
    inset: 0 0 0 300px;
  }
}
