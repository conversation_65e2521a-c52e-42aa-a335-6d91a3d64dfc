section.location-list {
  margin: 0 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1 0 auto;
}

section.location-list .empty-list {
  margin-top: 15px;
  text-align: center;
  width: 250px;
}

@media only screen and (width <=960px) {
  section.location-list .location-list-items {
    width: 100%;
  }
}

section.location-list .location-list-item {
  position: relative;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  color: #333;
  font-size: 12px;
  margin-bottom: 10px;
  min-height: 75px;
  word-break: break-word;
}

section.location-list .location-title {
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

section.location-list .location-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 72px;
  margin-right: 10px;
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  section.location-list .location-list-item {
    min-width: 480px;
    max-width: 600px;
  }
}
