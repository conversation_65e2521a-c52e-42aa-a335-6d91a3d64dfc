/* Create Car */

div.create-car {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  align-items: center;
  transform: translate3d(0, 0, 0);
  margin: 45px 0;
}

.car-form-wrapper {
  margin: 32px 0;
}

.car-form-title {
  text-align: center;
  text-transform: capitalize;
  color: #121212;
}

.checkbox-fc {
  margin: 13px 0 !important;
}

.checkbox-fc .checkbox-fcl {
  color: rgb(0 0 0 / 60%);
  font-size: 0.9em;
  line-height: 1em;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  .car-form {
    width: 360px;
    padding: 30px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  .car-form {
    width: 550px;
    padding: 30px;
  }
}
