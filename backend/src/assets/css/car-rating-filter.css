div.rating-filter div.filter-elements {
  padding: 7px 5px;
}

div.rating-filter div.filter-elements div.filter-element {
  margin: 5px;
  width: fit-content;
  display: flex;
  flex-direction: row;
  align-items: center;
}

div.rating-filter div.filter-elements div.filter-element input[type='radio'].rating-checkbox {
  cursor: pointer;
  margin-left: 5px;
}

div.rating-filter div.filter-elements div.filter-element span.ratings {
  cursor: pointer;
  display: inline-block;
  padding: 0 4px;
}

div.rating-filter div.filter-elements div.filter-element .rating {
  position: relative;
  bottom: -4px;
}

div.rating-filter div.filter-elements div.filter-element span.rating-text {
  font-size: 12px;
  color: #a3a3a3;
  margin-top: 4px;
}
