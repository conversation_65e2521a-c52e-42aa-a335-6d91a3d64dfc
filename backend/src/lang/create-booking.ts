import LocalizedStrings from 'localized-strings'
import * as langHelper from '@/common/langHelper'

const strings = new LocalizedStrings({
  fr: {
    NEW_BOOKING_HEADING: 'Nouvelle réservation',
    NEW_BOOKING: 'Nouvelle réservation',
    DRESS: 'Robe',
    DRIVER: 'Client',
    PICKUP_LOCATION: 'Lieu de prise en charge',
    DROP_OFF_LOCATION: 'Lieu de retour',
    FROM: 'De',
    TO: 'À',
    STATUS: 'Statut',
    PRICE: 'Prix',
    CANCELLATION: 'Annulation',
    AMENDMENTS: 'Modifications',
    PENDING: 'En attente',
    DEPOSIT: 'Acompte',
    PAID: 'Payé',
    RESERVED: 'Réservé',
    CANCELLED: 'Annulé',
  },
  en: {
    NEW_BOOKING_HEADING: 'New booking',
    NEW_BOOKING: 'New Booking',
    DRESS: 'Dress',
    DRIVER: 'Customer',
    PICKUP_LOCATION: 'Pickup Location',
    DROP_OFF_LOCATION: 'Drop-off Location',
    FROM: 'From',
    TO: 'To',
    STATUS: 'Status',
    PRICE: 'Price',
    CANCELLATION: 'Cancellation',
    AMENDMENTS: 'Amendments',
    PENDING: 'Pending',
    DEPOSIT: 'Deposit',
    PAID: 'Paid',
    RESERVED: 'Reserved',
    CANCELLED: 'Cancelled',
  },
  es: {
    NEW_BOOKING_HEADING: 'Nueva reserva',
    NEW_BOOKING: 'Nueva reserva',
    DRESS: 'Vestido',
    DRIVER: 'Cliente',
    PICKUP_LOCATION: 'Lugar de recogida',
    DROP_OFF_LOCATION: 'Lugar de entrega',
    FROM: 'Desde',
    TO: 'Hasta',
    STATUS: 'Estado',
    PRICE: 'Precio',
    CANCELLATION: 'Cancelación',
    AMENDMENTS: 'Modificaciones',
    PENDING: 'Pendiente',
    DEPOSIT: 'Depósito',
    PAID: 'Pagado',
    RESERVED: 'Reservado',
    CANCELLED: 'Cancelado',
  },
  ar: {
    NEW_BOOKING_HEADING: 'حجز جديد',
    NEW_BOOKING: 'حجز جديد',
    DRESS: 'الفستان',
    DRIVER: 'العميل',
    PICKUP_LOCATION: 'موقع الاستلام',
    DROP_OFF_LOCATION: 'موقع التسليم',
    FROM: 'من',
    TO: 'إلى',
    STATUS: 'الحالة',
    PRICE: 'السعر',
    CANCELLATION: 'الإلغاء',
    AMENDMENTS: 'التعديلات',
    PENDING: 'في الانتظار',
    DEPOSIT: 'عربون',
    PAID: 'مدفوع',
    RESERVED: 'محجوز',
    CANCELLED: 'ملغي',
  },
})

langHelper.setLanguage(strings)
export { strings }
