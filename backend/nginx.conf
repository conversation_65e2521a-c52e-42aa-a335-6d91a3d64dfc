#
# Limit the request rate to 50 requests per second per IP address to prevent DDoS attack
#
limit_req_zone $binary_remote_addr zone=mylimit:10m rate=50r/s;

#
# Set a response status code that is returned to rejected requests
#
limit_req_status 429;

server
{
	listen 3001;
	root /usr/share/nginx/html;
	index index.html;

	# Enable rate limiting to prevent Brute force attacks, DoS and DDoS attacks, and Web scraping
	limit_req zone=mylimit burst=100 nodelay;

	location /
	{
		# First attempt to serve request as file, then as directory,
		# then as index.html, then fall back to displaying a 404.
		try_files $uri $uri/ /index.html =404;
	}
}
