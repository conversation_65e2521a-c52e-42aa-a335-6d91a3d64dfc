#
# Limit the request rate to 50 requests per second per IP address to prevent DDoS attack
#
limit_req_zone $binary_remote_addr zone=mylimit:10m rate=50r/s;

#
# Set a response status code that is returned to rejected requests
#
limit_req_status 429;

#
# cdn
#
server
{
	listen 443 ssl;

	# RSA certificate
	ssl_certificate /etc/letsencrypt/live/teradev.dynv6.net/fullchain.pem;
	ssl_certificate_key /etc/letsencrypt/live/teradev.dynv6.net/privkey.pem;
	include /etc/letsencrypt/options-ssl-nginx.conf;
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

	# Enable rate limiting to prevent Brute force attacks, DoS and DDoS attacks, and Web scraping
	limit_req zone=mylimit burst=100 nodelay;

	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;

	server_name _;

	location /cdn
	{
		alias /var/www/cdn;
	}

	location /.well-known
	{
		alias /usr/share/nginx/html/.well-known;
	}

	location /
	{
		return 403;
	}

	error_page 404 /404.html;
	location = /404.html
	{
		root /usr/share/nginx/html;
		internal;
	}

	error_page 403 /403.html;
	location = /403.html
	{
		root /usr/share/nginx/html;
		internal;
	}

	error_page 500 502 503 504 /50x.html;
	location = /50x.html
	{
		root /usr/share/nginx/html;
		internal;
	}
}

#
# Redirect http to https
#
server
{
	listen 80;
	server_name _;
	return 301 https://$host$request_uri;
}

#
# bookcars backend
#
server
{
	root /var/www/bookcars/backend;
	listen 3001 ssl;

	# RSA certificate
	ssl_certificate /etc/letsencrypt/live/teradev.dynv6.net/fullchain.pem;
	ssl_certificate_key /etc/letsencrypt/live/teradev.dynv6.net/privkey.pem;
	include /etc/letsencrypt/options-ssl-nginx.conf;
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
	error_page 497 301 =307 https://$host:$server_port$request_uri;

	# Enable rate limiting to prevent Brute force attacks, DoS and DDoS attacks, and Web scraping
	limit_req zone=mylimit burst=100 nodelay;

	access_log /var/log/nginx/bookcars.backend.access.log;
	error_log /var/log/nginx/bookcars.backend.error.log;

	index index.html;

	location /
	{
		# First attempt to serve request as file, then as directory,
		# then as index.html, then fall back to displaying a 404.
		try_files $uri $uri/ /index.html =404;
	}
}

#
# bookcars frontend
#
server
{
	root /var/www/bookcars/frontend;
	listen 3002 ssl;

	# RSA certificate
	ssl_certificate /etc/letsencrypt/live/teradev.dynv6.net/fullchain.pem;
	ssl_certificate_key /etc/letsencrypt/live/teradev.dynv6.net/privkey.pem;
	include /etc/letsencrypt/options-ssl-nginx.conf;
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
	error_page 497 301 =307 https://$host:$server_port$request_uri;

	# Enable rate limiting to prevent Brute force attacks, DoS and DDoS attacks, and Web scraping
	limit_req zone=mylimit burst=100 nodelay;

	access_log /var/log/nginx/bookcars.frontend.access.log;
	error_log /var/log/nginx/bookcars.frontend.error.log;

	index index.html;

	location /
	{
		# First attempt to serve request as file, then as directory,
		# then as index.html, then fall back to displaying a 404.
		try_files $uri $uri/ /index.html =404;
	}
}
