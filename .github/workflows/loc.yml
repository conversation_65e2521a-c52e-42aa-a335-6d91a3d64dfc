on: [push]
name: Lines of Code

jobs:
  loc_job:
    runs-on: ubuntu-latest
    name: Run
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create the badge
        uses: shadowmoose/GHA-LoC-Badge@1.0.0
        id: badge
        with:
          debug: true
          directory: ./
          badge_label: lines of code
          badge_color: green
          badge: ./output/badge.svg
          ignore: 'node_modules|coverage|package-lock.json|README.md|LICENSE|CITATION.cff|.github|.vscode|__scripts|__services|.env|tsconfig.tsbuildinfo|dist'

      - name: Print the output
        run: |
          echo "Scanned: ${{ steps.badge.outputs.counted_files }}";
          echo "Line Count: ${{ steps.badge.outputs.total_lines }}";

      - name: Deploy to loc branch
        uses: peaceiris/actions-gh-pages@v3
        with:
          publish_dir: ./output
          publish_branch: loc
          github_token: ${{ secrets.GITHUB_TOKEN }}
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
