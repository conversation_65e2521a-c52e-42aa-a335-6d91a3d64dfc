NODE_ENV=production
BC_PORT=4002
BC_HTTPS=false
BC_PRIVATE_KEY=/etc/ssl/bookcars.key
BC_CERTIFICATE=/etc/ssl/bookcars.crt
BC_DB_URI="****************************************************************************"
BC_DB_SSL=false
BC_DB_SSL_CERT=/etc/ssl/bookcars.crt
BC_DB_SSL_CA=/etc/ssl/bookcars.ca.pem
BC_DB_DEBUG=false
BC_COOKIE_SECRET=COOKIE_SECRET
BC_AUTH_COOKIE_DOMAIN=localhost
BC_JWT_SECRET=JWT_SECRET
BC_JWT_EXPIRE_AT=86400
BC_TOKEN_EXPIRE_AT=86400
BC_SMTP_HOST=smtp.sendgrid.net
BC_SMTP_PORT=587
BC_SMTP_USER=apikey
BC_SMTP_PASS="PASSWORD"
BC_SMTP_FROM=<EMAIL>
BC_CDN_ROOT=/var/www/cdn
BC_CDN_USERS=/var/www/cdn/bookcars/users
BC_CDN_TEMP_USERS=/var/www/cdn/bookcars/temp/users
BC_CDN_CARS=/var/www/cdn/bookcars/cars
BC_CDN_TEMP_CARS=/var/www/cdn/bookcars/temp/cars
BC_CDN_LOCATIONS=/var/www/cdn/bookcars/locations
BC_CDN_TEMP_LOCATIONS=/var/www/cdn/bookcars/temp/locations
BC_CDN_CONTRACTS=/var/www/cdn/bookcars/contracts
BC_CDN_TEMP_CONTRACTS=/var/www/cdn/bookcars/temp/contracts
BC_CDN_LICENSES=/var/www/cdn/bookcars/licenses
BC_CDN_TEMP_LICENSES=/var/www/cdn/bookcars/temp/licenses
BC_DEFAULT_LANGUAGE=en
BC_BACKEND_HOST=http://localhost:3001/
BC_FRONTEND_HOST=http://localhost:8080/
BC_MINIMUM_AGE=21
BC_EXPO_ACCESS_TOKEN=EXPO_ACCESS_TOKEN
BC_STRIPE_SECRET_KEY=STRIPE_SECRET_KEY
BC_STRIPE_SESSION_EXPIRE_AT=82800
BC_PAYPAL_SANDBOX=true
BC_PAYPAL_CLIENT_ID=PAYPAL_CLIENT_ID
BC_PAYPAL_CLIENT_SECRET=PAYPAL_CLIENT_SECRET
BC_ADMIN_EMAIL=<EMAIL>
BC_RECAPTCHA_SECRET=RECAPTCHA_SECRET
BC_WEBSITE_NAME=BookCars
BC_TIMEZONE=UTC
BC_IPINFO_API_KEY=IPINFO_API_KEY
BC_IPINFO_DEFAULT_COUNTRY=US
