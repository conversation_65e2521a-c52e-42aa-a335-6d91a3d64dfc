FROM node:lts-alpine AS packages
WORKDIR /bookcars/packages

COPY ./packages /bookcars/packages

# Install dependencies for all internal packages
RUN set -e && \
  find ./ -mindepth 1 -maxdepth 1 -type d \
  -exec sh -c 'for dir; do \
    if [ -f "$dir/package.json" ]; then \
      echo "Installing dependencies in $dir"; \
      npm --prefix "$dir" install; \
    fi; \
  done' _ {} +


FROM node:lts-alpine AS build

WORKDIR /bookcars/api

COPY ./api/package*.json ./

RUN npm install --force && \
    npm cache clean --force

FROM node:lts-alpine
WORKDIR /bookcars/api

COPY --from=build /bookcars/api/node_modules ./node_modules

COPY --from=packages /bookcars/packages /bookcars/packages

COPY ./api ./
COPY ./api/.env.docker .env

EXPOSE 4002

CMD ["npm", "run", "dev"]
