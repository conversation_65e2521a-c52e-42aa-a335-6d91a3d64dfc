{"moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "roots": ["./__tests__/"], "testMatch": ["**/*.test.ts"], "collectCoverage": true, "coverageReporters": ["cobertura", "html"], "collectCoverageFrom": ["src/**/*.ts", "!**/node_modules/**", "!**/dist/**", "!**/__tests__/**"], "testTimeout": 180000, "globalSetup": "./dist/__tests__/globalSetup.js", "maxWorkers": 1}