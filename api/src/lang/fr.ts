import * as env from '../config/env.config'

export const fr = {
  ERROR: 'Erreur interne : ',
  DB_ERROR: 'Échec de la requête dans la base de données : ',
  SMTP_ERROR: "Erreur SMTP - Échec de l'envoi de l'email: ",
  ACCOUNT_ACTIVATION_SUBJECT: 'Activation de votre compte',
  HELLO: 'Bonjour ',
  ACCOUNT_ACTIVATION_LINK: 'Veuillez activer votre compte en cliquant sur le lien :',
  REGARDS: `Cordialement,<br>L'équipe ${env.WEBSITE_NAME}`,
  ACCOUNT_ACTIVATION_TECHNICAL_ISSUE: 'Problème technique! Veuillez cliquer sur renvoyer pour valider votre e-mail.',
  ACCOUNT_ACTIVATION_LINK_EXPIRED: 'Votre lien de validation a peut-être expiré. Veuillez cliquer sur renvoyer pour valider votre e-mail.',
  ACCOUNT_ACTIVATION_LINK_ERROR: "Nous n'avons pas pu trouver d'utilisateur correspondant à cette adresse e-mail. Veuillez vous inscrire.",
  ACCOUNT_ACTIVATION_SUCCESS: 'Votre compte a été validé avec succès.',
  ACCOUNT_ACTIVATION_RESEND_ERROR: "Nous n'avons pas pu trouver d'utilisateur correspondant à cette adresse e-mail. Assurez-vous que votre e-mail est correct.",
  ACCOUNT_ACTIVATION_ACCOUNT_VERIFIED: 'Ce compte a déjà été validé. Veuillez vous connecter.',
  ACCOUNT_ACTIVATION_EMAIL_SENT_PART_1: 'Un email de validation a été envoyé à',
  ACCOUNT_ACTIVATION_EMAIL_SENT_PART_2: ". Il expirera au bout d'un jour. Si vous n'avez pas reçu d'e-mail de validation, cliquez sur renvoyer.",
  CAR_IMAGE_REQUIRED: 'Le champ image de Car ne peut pas être vide: ',
  CAR_IMAGE_NOT_FOUND: 'Le fichier image est introuvable : ',
  PASSWORD_RESET_SUBJECT: 'Réinitialisation du mot de passe',
  PASSWORD_RESET_LINK: 'Veuillez réinitialiser votre mot de passe en cliquant sur le lien :',
  BOOKING_CONFIRMED_SUBJECT_PART1: 'Votre réservation',
  BOOKING_CONFIRMED_SUBJECT_PART2: 'a été confirmée.',
  BOOKING_CONFIRMED_PART1: 'Votre réservation',
  BOOKING_CONFIRMED_PART2: 'a bien été confirmée et le paiement a bien été effectué.',
  BOOKING_CONFIRMED_PART3: ' Veuillez vous rendre à notre agence ',
  BOOKING_CONFIRMED_PART4: ' (',
  BOOKING_CONFIRMED_PART5: ') le ',
  BOOKING_CONFIRMED_PART6: ` (${env.TIMEZONE}) pour récupérer votre véhicule `,
  BOOKING_CONFIRMED_PART7: '.',
  BOOKING_CONFIRMED_PART8: "Veuillez apporter avec vous votre pièce d'identité, votre permis de conduire et le chèque de garantie.",
  BOOKING_CONFIRMED_PART9: 'Vous devez rendre le véhicule à notre agence ',
  BOOKING_CONFIRMED_PART10: ' (',
  BOOKING_CONFIRMED_PART11: ') le ',
  BOOKING_CONFIRMED_PART12: ` (${env.TIMEZONE}).`,
  BOOKING_CONFIRMED_PART13: 'Veuillez respecter les dates et les horaires de prise en charge et de restitution du véhicule.',
  BOOKING_CONFIRMED_PART14: 'Vous pouvez suivre votre réservation sur : ',
  BOOKING_PAY_LATER_NOTIFICATION: 'a confirmé la réservation',
  BOOKING_PAID_NOTIFICATION: 'a payé la réservation',
  CANCEL_BOOKING_NOTIFICATION: "a fait une demande d'annulation de la réservation",
  BOOKING_UPDATED_NOTIFICATION_PART1: 'Le statut de la réservation',
  BOOKING_UPDATED_NOTIFICATION_PART2: 'a été mis à jour.',
  CONTACT_SUBJECT: 'Nouveau Message à partir du formulaire de Contact',
  SUBJECT: 'Objet',
  FROM: 'De',
  MESSAGE: 'Message',
  LOCATION_IMAGE_NOT_FOUND: 'Image de lieu introuvable',
  NEW_CAR_NOTIFICATION_PART1: 'Le fournisseur ',
  NEW_CAR_NOTIFICATION_PART2: ' a créé une nouvelle voiture.',
}
