{
  "eslint.validate": [
    "typescript",
    "javascript",
    "typescriptreact",
    "javascriptreact"
  ],
  "eslint.run": "onType",
  "eslint.workingDirectories": [
    { "directory": "api", "changeProcessCWD": true, "pattern": "**/*.{js,ts}" },
    { "directory": "backend", "changeProcessCWD": true, "pattern": "**/*.{js,jsx,ts,tsx}" },
    { "directory": "frontend", "changeProcessCWD": true, "pattern": "**/*.{js,jsx,ts,tsx}" },
    { "directory": "mobile", "changeProcessCWD": true, "pattern": "**/*.{js,jsx,ts,tsx}" },
  ],
  "files.insertFinalNewline": true
}
