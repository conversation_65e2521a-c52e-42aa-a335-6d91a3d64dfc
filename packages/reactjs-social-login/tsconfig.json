{
  "compilerOptions": {
    "outDir": "dist",
    "module": "esnext",
    "target": "ES2020",
    "lib": ["dom", "dom.iterable", "esnext"],
    "moduleResolution": "node",
    "jsx": "react",
    "sourceMap": true,
    "declaration": true,
    "esModuleInterop": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowSyntheticDefaultImports": true,
    "composite": true,
    "noEmitOnError": true,
    "baseUrl": "./src",
    "typeRoots": ["dist/src/index.d.ts"],
  },

  "include": ["src/**/*.ts", "src/**/*.tsx"],
  "exclude": ["node_modules", "dist"]
}
