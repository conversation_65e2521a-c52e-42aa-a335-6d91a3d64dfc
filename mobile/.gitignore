# OSX
#
.DS_Store
.DS_Store?
/**/.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof

# node.js
#
node_modules/
npm-debug.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/
web-build/
dist/

# misc
.env
.env.local
.env.dev
.env.development
.env.development.local
.env.test.local
.env.*production*
.env.oraclecloud

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
web-build/
dist/

google-services.json
/**/google-services.json
GoogleService-Info.plist
/**/GoogleService-Info.plist

# @end expo-cli

# yarn
.yarn/

# build
*.apk
/**/*.apk

android/
ios/

%ProgramData%/

*.tsbuildinfo

.eslintcache
