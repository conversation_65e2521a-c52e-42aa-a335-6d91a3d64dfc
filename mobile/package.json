{"name": "bookcars", "version": "7.2.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"install:dependencies": "cd ../packages/currency-converter && npm i && cd ../bookcars-helper && npm i", "eas-build-pre-install": "npm run install:dependencies", "ts:build": "npm run install:dependencies && tsc --build --verbose", "ts:check": "npm run ts:build", "start": "npm run ts:build && expo start --port 8082", "start:clean": "npm run ts:build && expo start -c --port 8082", "android": "npm run ts:build && expo run:android", "ios": "npm run ts:build && expo run:ios", "build:android": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile production --platform android", "build:android:local": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile production --platform android --local", "build:android:preview": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile preview --platform android", "build:ios": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile production --platform ios", "build:ios:local": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile production --platform ios --local", "build:ios:preview": "cross-env EAS_NO_VCS=1 npm run ts:build && eas build --profile preview --platform android", "lint": "eslint . --cache --cache-location .eslintcache", "fix": "eslint --fix .", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean"}, "dependencies": {"@babel/runtime": "^7.27.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/drawer": "^7.3.12", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.2", "@stripe/stripe-react-native": "0.45.0", "@types/lodash.debounce": "^4.0.9", "@types/mime": "^4.0.0", "@types/react-native-dotenv": "^0.2.2", "@types/react-native-vector-icons": "^6.4.18", "@types/validator": "^13.15.0", "axios": "^1.9.0", "axios-retry": "^4.5.0", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-asset": "~11.1.5", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.5", "expo-notifications": "~0.31.2", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.13", "i18n-js": "^4.5.1", "lodash.debounce": "^4.0.8", "mime": "^4.0.7", "prop-types": "^15.8.1", "react": "19.0.0", "react-native": "0.79.2", "react-native-animatable": "^1.4.0", "react-native-dotenv": "^3.4.11", "react-native-feather": "^1.1.2", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.3", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-size-matters": "^0.4.2", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "validator": "^13.15.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/prop-types": "^15.7.14", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "cross-env": "^7.0.3", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "typescript": "~5.8.3"}, "private": true}