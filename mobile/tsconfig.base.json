{"compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["es2017"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}