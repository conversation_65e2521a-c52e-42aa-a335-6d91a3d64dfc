﻿{
  "expo": {
    "jsEngine": "hermes",
    "newArchEnabled": true,
    "name": "BookCars",
    "version": "7.2.0",
    "slug": "bookcars",
    "icon": "./assets/icon.png",
    "assetBundlePatterns": [
      "**/*"
    ],
    "splash": {
      "backgroundColor": "#f37022",
      "image": "./assets/splash.png"
    },
    "android": {
      "versionCode": 720,
      "adaptiveIcon": {
        "foregroundImage": "./assets/icon.png"
      },
      "icon": "./assets/icon.png",
      "googleServicesFile": "./google-services.json",
      "softwareKeyboardLayoutMode": "pan",
      "package": "com.bookcars"
    },
    "ios": {
      "buildNumber": "7.2.0",
      "supportsTablet": true,
      "icon": "./assets/icon.png",
      "bundleIdentifier": "com.bookcars"
    },
    "plugins": [
      "expo-localization",
      "./plugins/usesCleartextTraffic",
      [
        "expo-splash-screen",
        {
          "backgroundColor": "#f37022",
          "image": "./assets/icon.png",
          "imageWidth": 200
        }
      ],
      [
        "@stripe/stripe-react-native",
        {
          "merchantIdentifier": "MERCHANT_IDENTIFIER",
          "enableGooglePay": true
        }
      ],
      [
        "expo-document-picker",
        {
          "iCloudContainerEnvironment": "Production"
        }
      ]
    ],
    "owner": "aelassas",
    "extra": {
      "eas": {
        "projectId": "fe1ab335-41ae-4a35-960b-0052b493db49"
      }
    },
    "updates": {
      "url": "https://u.expo.dev/fe1ab335-41ae-4a35-960b-0052b493db49"
    },
    "experiments": {
      "tsconfigPaths": true
    },
    "runtimeVersion": {
      "policy": "appVersion"
    }
  }
}

