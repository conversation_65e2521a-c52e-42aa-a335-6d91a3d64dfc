const env = {
  API_HOST: 'http://localhost:4002',
  WEBSITE_NAME: 'BookDresses',
  DEFAULT_LANGUAGE: 'en',
  BASE_CURRENCY: 'USD',
  PAGE_SIZE: 30,
  DRESSES_PAGE_SIZE: 15,
  BOOKINGS_PAGE_SIZE: 20,
  BOOKINGS_MOBILE_PAGE_SIZE: 10,
  CDN_USERS: '/images/users/',
  CDN_DRESSES: '/images/dresses/',
  CDN_LOCATIONS: '/images/locations/',
  CDN_LICENSES: '/images/licenses/',
  CDN_TEMP_LICENSES: '/images/temp-licenses/',
  SUPPLIER_IMAGE_WIDTH: 60,
  SUPPLIER_IMAGE_HEIGHT: 30,
  DRESS_IMAGE_WIDTH: 300,
  DRESS_IMAGE_HEIGHT: 200,
  RECAPTCHA_ENABLED: false,
  RECAPTCHA_SITE_KEY: '',
  MINIMUM_AGE: 18,
  PAGI<PERSON>TION_MODE: 'CLASSIC',
  PAYMENT_GATEWAY: 'stripe',
  STRIPE_PUBLISHABLE_KEY: '',
  PAYPAL_CLIENT_ID: '',
  PAYPAL_DEBUG: false,
  SET_LANGUAGE_FROM_IP: false,
  GOOGLE_ANALYTICS_ENABLED: false,
  GOOGLE_ANALYTICS_ID: '',
  CONTACT_EMAIL: '<EMAIL>',
  DEPOSIT_FILTER_VALUE_1: 500,
  DEPOSIT_FILTER_VALUE_2: 1000,
  DEPOSIT_FILTER_VALUE_3: 2000,
  FB_APP_ID: '',
  APPLE_ID: '',
  GG_APP_ID: '',
  MIN_LOCATIONS: 4,
  HIDE_SUPPLIERS: false,
  MIN_RENTAL_HOURS: 1,
  MIN_PICK_UP_HOURS: 1,
  MAP_LATITUDE: 34.0268755,
  MAP_LONGITUDE: 1.6528399999999976,
  MAP_ZOOM: 5,
  _LANGUAGES: [
    {
      code: 'en',
      label: 'English',
    },
    {
      code: 'fr',
      label: 'Français',
    },
    {
      code: 'ar',
      label: 'العربية',
    },
  ],
}

export default env
