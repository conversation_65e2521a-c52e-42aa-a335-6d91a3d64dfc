{"name": "bookcars", "version": "7.2.0", "description": "Car Rental Booking System", "keywords": ["react", "react-native", "mongodb", "stripe", "paypal", "car-rental", "car-rental-system", "aggregator", "bookcars"], "homepage": "https://github.com/aelassas/bookcars#readme", "bugs": {"url": "https://github.com/aelassas/bookcars/issues"}, "repository": {"type": "git", "url": "git+https://github.com/aelassas/bookcars.git"}, "funding": "https://github.com/sponsors/aelassas", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "type": "module", "main": "pre-commit.js", "scripts": {"pre-commit": "node pre-commit.js", "prepare": "husky"}, "devDependencies": {"chalk": "^5.4.1", "husky": "^9.1.7", "p-limit": "^6.2.0"}}