services:
  mongo:
    image: mongo:latest
    command: mongod --quiet --logpath /dev/null
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin
    ports:
      - 27018:27017
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb

  mongo-express:
    image: mongo-express:latest
    restart: always
    ports:
      - 8084:8081
    environment:
      ME_CONFIG_MONGODB_URL: *********************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      - mongo

  bc-dev-api:
    build:
      context: .
      dockerfile: ./api/Dockerfile.dev
    env_file: ./api/.env.docker
    restart: always
    ports:
      - 4002:4002
    depends_on:
      - mongo
    volumes:
      - cdn:/var/www/cdn/bookcars
      - ./api:/bookcars/api
      - /bookcars/api/node_modules
      - api_logs:/bookcars/api/logs

  bc-dev-backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile.dev
    env_file: ./backend/.env.docker
    restart: always
    depends_on:
      - bc-dev-api
    ports:
      - 3001:3001
    volumes:
      - ./backend:/bookcars/backend
      - /bookcars/backend/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true

  bc-dev-frontend:
    build:
      context: .
      dockerfile: ./frontend/Dockerfile.dev
    env_file: ./frontend/.env.docker
    restart: always
    depends_on:
      - bc-dev-api
    ports:
      - 8080:8080
      - 8443:443
    volumes:
      - ./frontend:/bookcars/frontend
      - /bookcars/frontend/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true

volumes:
  cdn:
  mongodb_data:
  mongodb_config:
  api_logs:
