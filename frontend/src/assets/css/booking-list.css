div.bs-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

div.bs-list .data-grid {
  width: 100%;
}

div.bs-list .empty-list {
  text-align: center;
  width: 250px;
}

div.bs-list div.cell-supplier {
  display: inline-flex;
  align-items: center;
  width: 60px;
  height: 30px;
  position: relative;
  vertical-align: middle;

}

div.bs-list div.cell-supplier img {
  max-width: 100%;
  max-height: 100%;
}

div.bs-update-status {
  width: 444px;
  height: 70px;
  overflow: hidden;
}

div.bs-list span.bp {
  font-weight: 600;
}

div.bs-list div.booking-details {
  display: table;
  color: #333;
  font-size: 15px;
  background-color: #fff;
  margin: 10px;
  padding: 10px;
  border: 1px solid #d9d8d9;
  border-radius: 5px;
  width: calc(100% - 20px);
}

div.bs-list div.booking-details div.booking-detail {
  display: table-row;
}

div.bs-list div.booking-details div.booking-detail div.booking-detail-value {
  display: table-row;
  line-height: 25px;
  font-size: 13px;
  color: rgb(0 0 0 / 47%);
}

div.bs-list div.booking-details div.booking-detail span.booking-detail-title {
  display: table-row;
  font-weight: 700;
  width: 250px;
  font-size: 15px;
  color: rgb(0 0 0 / 87%);
}

div.bs-list div.booking-details div.booking-detail div.booking-detail-value div.dress-supplier {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  width: 60px;
  height: 30px;
}

div.bs-list div.booking-details div.booking-detail div.booking-detail-value div.dress-supplier img {
  max-width: 100%;
  max-height: 100%;
}

div.bs-list div.booking-details div.booking-detail div.booking-detail-value span.dress-supplier-name {
  color: rgb(0 0 0 / 60%);
  font-size: 0.9em;
  line-height: 1em;
  margin-left: 5px;
}

div.bs-list div.booking-details div.booking-detail div.booking-price {
  font-size: 17px;
  font-weight: 700;
  color: rgb(0 0 0 / 87%);
}

div.bs-list div.booking-details div.bs {
  padding: 3px;
  margin-bottom: 5px;
  width: 100%;
  text-align: center;
  color: #fff;
  display: inline-flex;
  height: 28px;
  font-size: 12px;
  font-weight: 400;
  justify-content: center;
  align-items: center;
  border-radius: 18px;
}

div.bs-list div.booking-details div.bs-void {
  background: #D9D9D9;
  color: #6E7C86;
}

div.bs-list div.booking-details div.bs-pending {
  background: #FBDCC2;
  color: #EF6C00;
}

div.bs-list div.booking-details div.bs-deposit {
  background: #CDECDA;
  color: #3CB371;
}

div.bs-list div.booking-details div.bs-paid {
  background: #D1F9D1;
  color: #77BC23;
}

div.bs-list div.booking-details div.bs-reserved {
  background: #D9E7F4;
  color: #1E88E5;
}

div.bs-list div.booking-details div.bs-cancelled {
  background: #FBDFDE;
  color: #E53935;
}

div.bs-list div.bs-buttons {
  display: grid;
  margin-top: 10px;
}

div.bs-list div.bs-buttons a,
div.bs-list div.bs-buttons button {
  width: 100%;
  margin-bottom: 5px;
}

@media only screen and (width <=960px) {
  div.bs-list {
    max-width: 480px;
  }

  div.bs-list .empty-list {
    margin-top: 10px;
  }
}

@media only screen and (width >=960px) {
  div.bs-list {
    position: absolute;
    inset: 0;
    overflow-y: auto;
  }

  div.bs-list .empty-list {
    margin-top: 15px;
  }
}
