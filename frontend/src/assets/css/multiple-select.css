li.ms-option {
  display: flex;
  align-items: center;
  width: 100%;
}

/* .ms-option .Mui-focused, */
.ms-option:focus,
.ms-option:focus-visible,
.ms-option:focus-within {
  background: #eee !important;
}

li.ms-option:hover {
  background: #eee;
  cursor: pointer;
}

.option-image {
  margin-top: 5px;
}

.option-car {
  height: 75px;
}

.option-name {
  color: #333;
  display: inline-block;
  font-size: 0.9em;
  line-height: 1em;
  white-space: nowrap;
  margin-left: 5px;
  margin-bottom: 5px;
}

.car-option-name {
  color: #333;
  display: inline-block;
  font-size: 1em;
  font-weight: 600;
  white-space: nowrap;
  margin-left: 5px;
  margin-bottom: 5px;
}

.suo {
  margin-bottom: 3px;
}
