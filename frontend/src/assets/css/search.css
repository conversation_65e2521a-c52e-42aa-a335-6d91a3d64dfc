div.search {
  display: flex;
  flex: 1 0 auto;
  transform: translate3d(0, 0, 0);
}

div.search div.col-1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 5px;
}

div.search div.col-1 .map {
  margin-top: 20px;
  min-height: 0;
  height: 180px;
  margin-bottom: 3px;
}

div.search div.col-1 .filter {
  margin: 5px 0;
  width: 100%;
  max-width: 480px;
}

div.search .btn-filters {
  max-width: 480px;
  margin-top: 5px;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.search {
    flex: 0 1 auto;
    flex-direction: column;
  }

  div.search div.col-1 {
    padding: 5px;
    margin-bottom: 5px;
  }

  div.search div.col-2 {
    padding: 1px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.search {
    flex-direction: row;
  }

  div.search div.col-1 {
    width: 300px;
    margin-left: 20px;
  }

  div.search div.col-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 0 auto;
  }
}
