div.supplier-list {
  display: flex;
  flex-wrap: wrap;
}

@media only screen and (width >=960px) {
  div.supplier-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}

@media only screen and (width <=960px) {
  div.supplier-list {
    justify-content: center;
  }
}

div.supplier-list div.supplier {
  width: 120px;
  height: 120px;
  margin: 20px;
  padding: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}


div.supplier-list div.supplier div.img {
  width: 72px;
  height: 52px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

div.supplier-list div.supplier div.img img {
  max-width: 100%;
  max-height: 100%;
}

div.supplier-list div.supplier div.name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 42px;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}
