/* Create settings */
div.settings {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  justify-content: center;
  align-items: center;
  margin: 45px 0;
  transform: translate3d(0, 0, 0);
  margin: 80px 0 200px 0;
  min-height: 100vh;
}

div.settings .settings-form-wrapper {
  margin: 32px 0;
}

div.settings .settings-form-title {
  text-align: center;
  text-transform: capitalize;
  color: #232323;
}

div.settings .settings-net-wrapper {
  margin: 32px 0;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.settings .settings-form {
    width: 360px;
    padding: 30px;
  }

  div.settings .settings-ctn {
    height: 240px;
  }

  div.settings .settings-net {
    width: 360px;
    height: 200px;
    padding: 30px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.settings .settings-form {
    width: 550px;
    padding: 30px;
  }

  div.settings .settings-ctn {
    height: 190px;
  }

  div.settings .settings-net {
    width: 550px;
    height: 200px;
    padding: 30px;
  }
}
