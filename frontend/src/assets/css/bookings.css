div.bookings {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  inset: 64px 0 0;
  transform: translate3d(0, 0, 0);
}

div.bookings div.col-1 {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 300px;
  padding: 12px 0 0 12px;
  background-color: #FAFAFA;
  overflow: auto;
}

div.bookings div.col-1 .cl-supplier-filter,
div.bookings div.col-1 .cl-status-filter,
div.bookings div.col-1 .cl-booking-filter {
  margin: 10px 10px 10px 0;
}

div.bookings div.col-1 .cl-booking-filter div.panel,
div.bookings div.col-1 .cl-booking-filter div.panel-collapse {
  padding-right: 15px;
  padding-left: 15px;
}

div.bookings div.col-1 .cl-status-filter,
div.bookings div.col-1 .cl-booking-filter {
  margin-bottom: 10px;
}

div.bookings div.col-2 {
  position: absolute;
  inset: 0 0 0 300px;
}

/* Device width is lower than or equal to 960px */

@media only screen and (width <=960px) {
  div.bookings {
    position: relative;
    top: 0;
    overflow-y: auto;
  }

  div.bookings div.col-1,
  div.bookings div.col-2 {
    width: 100%;
    max-width: 480px;
  }

  div.bookings div.col-1 {
    position: relative;
    inset: 0;
    padding-top: 15px;
  }

  div.bookings div.col-2 {
    position: relative;
    inset: 0;
  }

  div.bookings div.col-1 .cl-supplier-filter label.accordion,
  div.bookings div.col-1 .cl-status-filter label.accordion,
  div.bookings div.col-1 .cl-booking-filter label.accordion {
    background: #fff;
  }

  div.bookings div.col-1 .cl-supplier-filter,
  div.bookings div.col-1 .cl-status-filter,
  div.bookings div.col-1 .cl-booking-filter {
    background-color: #fff;
    width: calc(100% - 10px);
    max-width: 480px;
  }

  div.bookings div.col-1 .cl-booking-filter div.panel,
  div.bookings div.col-1 .cl-booking-filter div.panel-collapse {
    padding-right: 15px;
    padding-left: 15px;
  }

  div.bookings div.col-1 .cl-new-booking {
    width: calc(100% - 20px);
    max-width: 480px;
    margin: 15px 10px 5px;
  }
}
