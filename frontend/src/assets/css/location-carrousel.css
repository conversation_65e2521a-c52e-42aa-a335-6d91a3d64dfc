div.location-caroussel .slick-dots {
  bottom: -60px;
}

div.location-caroussel .slick-dots li.slick-active button::before {
  /* color: #1976D2; */
  color: #1a1a1a;
}

div.location-caroussel .btn-slider {
  color: #000 !important;
}

div.location-caroussel .btn-slider-prev {
  margin-right: 60px;
}

div.location-caroussel .btn-slider-next {
  margin-left: 60px;
}

div.location-caroussel div.box {
  width: 346px !important;
  height: 346px;
  background-color: #fff;
  display: flex !important;
  flex-direction: column;
  margin-right: 30px;
}

div.location-caroussel div.box div.location-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 346px;
  height: 140px;
}

div.location-caroussel div.box div.location-image img {
  max-width: 100%;
  max-height: 100%;
}

div.location-caroussel div.box div.location-image .location-icon {
  color: #939393;
}

div.location-caroussel div.box div.title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding-right: 12px;
}

div.location-caroussel div.box div.title .title-badge {
  margin-top: 13px;
}

div.location-caroussel div.box h2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

div.location-caroussel div.box h2,
div.location-caroussel div.box p {
  margin: 5px 13px;
}

div.location-caroussel div.box .btn-location {
  align-self: flex-start;
  margin-top: auto;
  margin-bottom: 6px;
  margin-left: 5px;
}

@media only screen and (width <=960px) {
  div.location-caroussel .btn-slider-prev {
    margin-right: 20px;
  }

  div.location-caroussel .btn-slider-next {
    margin-left: 20px;
  }

  div.location-caroussel div.box {
    margin: 0 auto;
  }
}
