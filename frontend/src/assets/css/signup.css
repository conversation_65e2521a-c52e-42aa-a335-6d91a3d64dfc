/* Signup */
div.signup {
  display: flex;
  flex-direction: row;
  flex: 1 0 auto;
  justify-content: center;
  margin: 120px 0 140px 0;
  transform: translate3d(0, 0, 0);
  min-height: 100vh;
}

div.signup .signup-form {
  height: fit-content;
}

div.signup .signup-form-title {
  text-align: center;
  text-transform: capitalize;
  color: #232323;
}

div.signup div.signup-tos {
  margin-top: 10px;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.signup .signup-form {
    width: 350px;
    padding: 30px;
  }

  div.signup .recaptcha {
    margin-top: 25px;
    margin-left: -10px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.signup .signup-form {
    width: 550px;
    padding: 30px;
  }

  div.signup .recaptcha {
    margin-top: 25px;
    margin-left: 85px;
  }
}
