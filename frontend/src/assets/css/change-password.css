/* Passwprd Reset */

div.password-reset {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translate3d(0, 0, 0);
}

div.password-reset .password-reset-form-title {
  text-align: center;
  text-transform: capitalize;
  margin-top: 40px;
  color: #232323;
}

div.password-reset .password-reset-form {
  margin: 100px 0 300px;
}

div.password-reset .password-reset-form div.buttons {
  margin: 20px 0 40px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.password-reset .password-reset-form {
    width: 330px;
    padding: 30px;
  }

  div.password-reset .password-reset-form div.buttons {
    display: flex;
    flex-direction: column;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.password-reset .password-reset-form {
    width: 450px;
    padding: 30px;
  }
}
