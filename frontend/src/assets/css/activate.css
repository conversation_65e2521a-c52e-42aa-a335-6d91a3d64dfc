/* Activate */
div.activate {
  display: flex;
  flex-direction: row;
  flex: 1 0 auto;
  justify-content: center;
  margin: 120px 0;
  transform: translate3d(0, 0, 0);
  min-height: 100vh;
}

div.activate h1.activate-form-title {
  text-align: center;
  margin-top: 0;
}

div.resend {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 120px 0;
  transform: translate3d(0, 0, 0);
  flex: 1 0 auto;
  padding-bottom: 40px;
  min-height: 100vh;
}

.activate-form h1,
.resend-form h1 {
  text-align: center;
  color: #232323;
}

.resend-form div.resend-form-content {
  display: flex;
  flex: 1 0 auto;
  flex-direction: column;
  align-items: center;
}

.resend-form div.resend-form-content span {
  float: left;
}

.resend-form div.resend-form-content .btn-resend {
  float: left;
  clear: left;
  width: 90px;
  margin-top: 15px !important;
}

.resend-form div.resend-form-content p.go-to-home {
  display: flex;
  flex: 1 0 auto;
  flex-direction: column;
  align-items: center;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  .activate-form {
    margin-top: 40px;
    width: 330px;
    height: 440px;
    padding: 30px;
  }

  .resend-form {
    margin-top: 40px;
    width: 330px;
    height: 330px;
    padding: 30px;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  .activate-form {
    margin-top: 40px;
    width: 450px;
    height: 390px;
    padding: 30px;
  }

  .resend-form {
    margin-top: 40px;
    width: 430px;
    height: 270px;
    padding: 30px;
  }
}
