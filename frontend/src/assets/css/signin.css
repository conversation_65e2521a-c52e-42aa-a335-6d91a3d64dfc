/* Signin */
div.signin {
  display: flex;
  flex-direction: row;
  flex: 1 0 auto;
  justify-content: center;
  margin: 120px 0;
  transform: translate3d(0, 0, 0);
  min-height: 100vh;
}

div.signin .signin-form {
  height: fit-content;
}

div.signin .signin-form-title {
  text-align: center;
  text-transform: capitalize;
  margin-top: 35px;
  color: #232323;
}

div.signin div.signin-buttons {
  float: right;
  margin-top: 20px;
}

div.signin .form-error {
  clear: both;
  height: 65px;
  padding-top: 10px;
  text-align: center;
}

div.signin div.stay-connected {
  margin-top: 15px;
  display: flex;
  align-items: center;
}

div.signin div.stay-connected input[type='checkbox'] {
  margin-right: 5px;
}

div.signin div.stay-connected label {
  cursor: pointer;
  color: rgb(0 0 0 / 60%);
  user-select: none;
}

div.signin div.forgot-password-wrapper {
  margin-top: 15px;
}

/* Device width is less than or equal to 960px */

@media only screen and (width <=960px) {
  div.signin .signin-form {
    align-items: center;
    justify-content: center;
    display: flex;
    width: 350px;
    border: 1px solid rgb(0 0 0 / 12%);
    padding: 15px;
  }

  div.signin div.signin-buttons {
    margin-top: 15px;
    margin-bottom: 5px;
    display: grid;
    width: 100%;
  }

  div.signin div.signin-buttons button {
    margin-right: 0;
  }
}

/* Device width is greater than or equal to 960px */

@media only screen and (width >=960px) {
  div.signin .signin-form {
    align-items: center;
    justify-content: center;
    display: flex;
    width: 380px;
    border: 1px solid rgb(0 0 0 / 12%);
    padding: 15px;
  }
}
