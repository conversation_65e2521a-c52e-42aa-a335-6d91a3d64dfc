import React, { lazy, Suspense, useEffect, useState } from 'react'
import { createBrowserRouter, RouterProvider, Outlet, useLocation } from 'react-router-dom'
import env from '@/config/env.config'
import { NotificationProvider } from '@/context/NotificationContext'
import { UserProvider } from '@/context/UserContext'
import { RecaptchaProvider } from '@/context/RecaptchaContext'
import { PayPalProvider } from '@/context/PayPalContext'
import { init as initGA } from '@/common/ga4'
import ScrollToTop from '@/components/ScrollToTop'
import NProgressIndicator from '@/components/NProgressIndicator'
import * as UserService from '@/services/UserService'

if (env.GOOGLE_ANALYTICS_ENABLED) {
  initGA()
}

const Header = lazy(() => import('@/components/Header'))
const SignIn = lazy(() => import('@/pages/SignIn'))
const SignUp = lazy(() => import('@/pages/SignUp'))
const Activate = lazy(() => import('@/pages/Activate'))
const ForgotPassword = lazy(() => import('@/pages/ForgotPassword'))
const ResetPassword = lazy(() => import('@/pages/ResetPassword'))
const Home = lazy(() => import('@/pages/Home'))
// Client-facing imports commented out
// const Search = lazy(() => import('@/pages/Search'))
// const Checkout = lazy(() => import('@/pages/Checkout'))
// const CheckoutSession = lazy(() => import('@/pages/CheckoutSession'))
// const Bookings = lazy(() => import('@/pages/Bookings'))
// const Booking = lazy(() => import('@/pages/Booking'))
const Settings = lazy(() => import('@/pages/Settings'))
const Notifications = lazy(() => import('@/pages/Notifications'))
const ToS = lazy(() => import('@/pages/ToS'))
const Privacy = lazy(() => import('@/pages/Privacy'))
const About = lazy(() => import('@/pages/About'))
const ChangePassword = lazy(() => import('@/pages/ChangePassword'))
const Contact = lazy(() => import('@/pages/Contact'))
const NoMatch = lazy(() => import('@/pages/NoMatch'))
const Locations = lazy(() => import('@/pages/Locations'))
const Suppliers = lazy(() => import('@/pages/Suppliers'))
const Faq = lazy(() => import('@/pages/Faq'))
const CookiePolicy = lazy(() => import('@/pages/CookiePolicy'))
const Dresses = lazy(() => import('@/pages/Dresses'))
// const Dress = lazy(() => import('@/pages/Dress')) // Client-facing dress view
const CreateDress = lazy(() => import('@/pages/CreateDress'))
const UpdateDress = lazy(() => import('@/pages/UpdateDress'))
const DressAnalytics = lazy(() => import('@/pages/DressAnalytics'))

const AppLayout = () => {
  const location = useLocation()
  const [refreshKey, setRefreshKey] = useState(0) // refreshKey to check user and notifications when navigating between routes
  const [isRTL, setIsRTL] = useState(false)

  useEffect(() => {
    // Set document direction based on language
    const language = UserService.getLanguage()
    const rtl = language === 'ar'
    setIsRTL(rtl)
    document.documentElement.setAttribute('dir', rtl ? 'rtl' : 'ltr')
    document.body.setAttribute('dir', rtl ? 'rtl' : 'ltr')
  }, [])

  useEffect(() => {
    setRefreshKey((prev) => prev + 1)
  }, [location.pathname])

  return (
    <UserProvider refreshKey={refreshKey}>
      <NotificationProvider refreshKey={refreshKey}>
        <RecaptchaProvider>
          <PayPalProvider>
            <ScrollToTop />
            <div className="app">
              <Suspense fallback={<NProgressIndicator />}>
                <Header />
                <Outlet />
              </Suspense>
            </div>
          </PayPalProvider>
        </RecaptchaProvider>
      </NotificationProvider>
    </UserProvider>
  )
}

const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout />,
    children: [
      { index: true, element: <Home /> },
      { path: 'sign-in', element: <SignIn /> },
      { path: 'sign-up', element: <SignUp /> },
      { path: 'activate', element: <Activate /> },
      { path: 'forgot-password', element: <ForgotPassword /> },
      { path: 'reset-password', element: <ResetPassword /> },
      // Client-facing routes commented out - focusing on admin functionality
      // { path: 'search', element: <Search /> },
      // { path: 'checkout', element: <Checkout /> },
      // { path: 'checkout-session/:sessionId', element: <CheckoutSession /> },
      // { path: 'bookings', element: <Bookings /> },
      // { path: 'booking', element: <Booking /> },
      { path: 'settings', element: <Settings /> },
      { path: 'notifications', element: <Notifications /> },
      { path: 'change-password', element: <ChangePassword /> },
      { path: 'about', element: <About /> },
      { path: 'tos', element: <ToS /> },
      { path: 'privacy', element: <Privacy /> },
      { path: 'contact', element: <Contact /> },
      { path: 'locations', element: <Locations /> },
      { path: 'faq', element: <Faq /> },
      { path: 'cookie-policy', element: <CookiePolicy /> },
      { path: 'dresses', element: <Dresses /> },
      // { path: 'dress', element: <Dress /> }, // Client-facing dress view
      { path: 'create-dress', element: <CreateDress /> },
      { path: 'update-dress', element: <UpdateDress /> },
      { path: 'dress-analytics', element: <DressAnalytics /> },
      ...(env.HIDE_SUPPLIERS ? [] : [{ path: 'suppliers', element: <Suppliers /> }]),
      { path: '*', element: <NoMatch /> }
    ]
  }
])

const App = () => <RouterProvider router={router} />

export default App
