FROM node:lts-alpine AS packages
WORKDIR /bookcars/packages

COPY ./packages /bookcars/packages

# Install dependencies for all internal packages
RUN set -e && \
  find ./ -mindepth 1 -maxdepth 1 -type d \
  -exec sh -c 'for dir; do \
    if [ -f "$dir/package.json" ]; then \
      echo "Installing dependencies in $dir"; \
      npm --prefix "$dir" install; \
    fi; \
  done' _ {} +


FROM node:lts-alpine AS frontend
WORKDIR /bookcars/frontend

COPY ./frontend/package*.json ./
RUN npm install --force && npm cache clean --force

COPY ./frontend ./ 
COPY ./frontend/.env.docker .env

# Copy preinstalled internal packages
COPY --from=packages /bookcars/packages /bookcars/packages

EXPOSE 8080
CMD ["npm", "run", "dev:docker"]
