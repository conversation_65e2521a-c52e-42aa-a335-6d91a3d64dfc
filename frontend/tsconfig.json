{
  "compilerOptions": {
    "composite": true,
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    "typeRoots": ["./node_modules/@types", "./types", "./src/types"],
    
    "paths": {
      "@/*": ["./src/*"],
      ":bookcars-types": ["../packages/bookcars-types"],
      ":bookcars-helper": ["../packages/bookcars-helper"],
      ":disable-react-devtools": ["../packages/disable-react-devtools"],
      ":reactjs-social-login": ["../packages/reactjs-social-login"],
    },
  },
  "include": ["src", ".env.d.ts"],
  "references": [
    { "path": "./tsconfig.node.json" },
    { "path": "../packages/bookcars-types" },
    { "path": "../packages/bookcars-helper" },
    { "path": "../packages/disable-react-devtools" },
    { "path": "../packages/reactjs-social-login" },
  ]
}
